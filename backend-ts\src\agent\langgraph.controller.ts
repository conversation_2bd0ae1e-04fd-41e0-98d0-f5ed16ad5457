import {
  <PERSON>,
  Post,
  Body,
  HttpException,
  HttpStatus,
  <PERSON>s,
  Req,
  Get,
  Param,
  UsePipes,
} from '@nestjs/common';
import { FastifyReply, FastifyRequest } from 'fastify';
import { GraphService } from './graph.service';
import { OverallState } from './state';
import { BaseMessage, HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { v4 as uuidv4 } from 'uuid';
import {
  LangGraphStreamRequestDto,
  LangGraphStreamRequestSchema,
  CreateThreadRequestDto,
  CreateThreadRequestSchema,
} from './dto/agent.dto';
import { ZodValidationPipe } from '../common/validation.pipe';

/**
 * Assistant information interface
 */
interface AssistantInfo {
  assistant_id: string;
  graph_id: string;
  created_at: string;
  updated_at: string;
  config: Record<string, any>;
  metadata: Record<string, any>;
}

/**
 * Thread information interface
 */
interface ThreadInfo {
  thread_id: string;
  created_at: string;
  updated_at: string;
  metadata: Record<string, any>;
}

/**
 * Run request body interface
 */
interface RunRequestBody {
  assistant_id: string;
  input?: {
    messages?: BaseMessage[];
    [key: string]: any;
  };
  config?: {
    configurable?: Record<string, any>;
    [key: string]: any;
  };
  stream_mode?: string[];
}

/**
 * LangGraph-compatible controller that provides streaming API endpoints.
 * This controller mimics the LangGraph server API that the frontend expects.
 */
@Controller()
export class LangGraphController {
  constructor(private readonly graphService: GraphService) {}

  /**
   * Get assistant information - required by LangGraph SDK
   */
  @Get('assistants/:assistantId')
  async getAssistant(@Param('assistantId') assistantId: string): Promise<AssistantInfo> {
    if (assistantId !== 'agent') {
      throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
    }

    return {
      assistant_id: 'agent',
      graph_id: 'pro-search-agent',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      config: {},
      metadata: {},
    };
  }

  /**
   * Get assistant graph - required by LangGraph SDK
   */
  @Get('assistants/:assistantId/graph')
  async getAssistantGraph(@Param('assistantId') assistantId: string) {
    if (assistantId !== 'agent') {
      throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
    }

    // Return a simplified graph representation
    return {
      nodes: [
        { id: 'generate_query', type: 'node' },
        { id: 'web_research', type: 'node' },
        { id: 'reflection', type: 'node' },
        { id: 'finalize_answer', type: 'node' },
      ],
      edges: [
        { source: '__start__', target: 'generate_query' },
        { source: 'generate_query', target: 'web_research' },
        { source: 'web_research', target: 'reflection' },
        { source: 'reflection', target: 'finalize_answer' },
        { source: 'finalize_answer', target: '__end__' },
      ],
    };
  }

  /**
   * Create a new thread - required by LangGraph SDK
   */
  @Post('threads')
  @UsePipes(new ZodValidationPipe(CreateThreadRequestSchema))
  async createThread(@Body() body: CreateThreadRequestDto): Promise<ThreadInfo> {
    const threadId = uuidv4();
    return {
      thread_id: threadId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: body.metadata || {},
    };
  }

  /**
   * Get thread information - required by LangGraph SDK
   */
  @Get('threads/:threadId')
  async getThread(@Param('threadId') threadId: string) {
    return {
      thread_id: threadId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: {},
    };
  }



  /**
   * Stream runs - this is the main endpoint for streaming agent execution
   */
  @Post('threads/:threadId/runs/stream')
  async streamRun(
    @Param('threadId') threadId: string,
    @Body(new ZodValidationPipe(LangGraphStreamRequestSchema)) body: LangGraphStreamRequestDto,
    @Res({ passthrough: false }) reply: FastifyReply,
    @Req() request: FastifyRequest,
  ) {
    try {
      // Validate assistant ID
      if (body.assistant_id !== 'agent') {
        throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
      }

      // Set up Server-Sent Events headers
      reply.raw.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': '*',
        'Access-Control-Allow-Methods': '*',
      });

      // Helper function to send SSE events
      const sendEvent = (event: string, data: any) => {
        const eventData = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
        reply.raw.write(eventData);
      };

      // Extract and convert messages from input
      const inputMessages = body.input?.messages || [];
      if (inputMessages.length === 0) {
        throw new HttpException('No messages provided', HttpStatus.BAD_REQUEST);
      }

      // Convert messages to LangChain BaseMessage format
      const messages: BaseMessage[] = inputMessages.map((msg: any) => {
        switch (msg.type) {
          case 'human':
            return new HumanMessage(msg.content);
          case 'ai':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // Create initial state
      const initialState: OverallState = {
        messages,
        query_list: [],
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: body.config?.configurable?.initial_search_query_count || 3,
        max_research_loops: body.config?.configurable?.max_research_loops || 2,
        research_loop_count: 0,
        reasoning_model: body.config?.configurable?.reasoning_model || 'gemini-2.5-pro-preview-05-06',
      };

      // Send initial metadata event
      sendEvent('metadata', {
        run_id: uuidv4(),
        thread_id: threadId,
        assistant_id: body.assistant_id,
      });

      // Get the graph and stream execution
      const graph = this.graphService.getGraph();
      
      // Stream the graph execution
      const stream = await graph.stream(initialState, {
        configurable: body.config?.configurable || {},
        streamMode: body.stream_mode || ['values'],
      });

      for await (const chunk of stream) {
        // Send the chunk as an event
        sendEvent('data', chunk);
        
        // Add small delay to prevent overwhelming the client
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // Send end event
      sendEvent('end', {});
      
      // Close the connection
      reply.raw.end();

    } catch (error) {
      console.error('Error in stream run:', error);
      
      // Send error event if connection is still open
      if (!reply.raw.destroyed) {
        const errorEvent = `event: error\ndata: ${JSON.stringify({
          error: error instanceof Error ? error.message : 'Unknown error',
        })}\n\n`;
        reply.raw.write(errorEvent);
        reply.raw.end();
      }
    }
  }

  /**
   * Non-streaming run endpoint for compatibility
   */
  @Post('threads/:threadId/runs')
  async createRun(
    @Param('threadId') threadId: string,
    @Body() body: {
      assistant_id: string;
      input?: any;
      config?: any;
    },
  ) {
    try {
      // Validate assistant ID
      if (body.assistant_id !== 'agent') {
        throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
      }

      // Extract and convert messages from input
      const inputMessages = body.input?.messages || [];
      if (inputMessages.length === 0) {
        throw new HttpException('No messages provided', HttpStatus.BAD_REQUEST);
      }

      // Convert messages to LangChain BaseMessage format
      const messages: BaseMessage[] = inputMessages.map((msg: any) => {
        switch (msg.type) {
          case 'human':
            return new HumanMessage(msg.content);
          case 'ai':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // Create initial state
      const initialState: OverallState = {
        messages,
        query_list: [],
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: body.config?.configurable?.initial_search_query_count || 3,
        max_research_loops: body.config?.configurable?.max_research_loops || 2,
        research_loop_count: 0,
        reasoning_model: body.config?.configurable?.reasoning_model || 'gemini-2.5-pro-preview-05-06',
      };

      // Get the graph and run it
      const graph = this.graphService.getGraph();
      const result = await graph.invoke(initialState, {
        configurable: body.config?.configurable || {},
      });

      return {
        run_id: uuidv4(),
        thread_id: threadId,
        assistant_id: body.assistant_id,
        status: 'success',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        output: result,
      };

    } catch (error) {
      console.error('Error in create run:', error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Unknown error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get run information - required by LangGraph SDK
   */
  @Get('threads/:threadId/runs/:runId')
  async getRun(
    @Param('threadId') threadId: string,
    @Param('runId') runId: string,
  ) {
    return {
      run_id: runId,
      thread_id: threadId,
      assistant_id: 'agent',
      status: 'success',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Get thread history - required by LangGraph SDK
   */
  @Post('threads/:threadId/history')
  async getThreadHistory(
    @Param('threadId') threadId: string,
    @Body() body: any,
  ) {
    // In a real implementation, this would retrieve the thread history from a database
    // For now, we'll return an empty history since we don't have persistent storage
    // The LangGraph SDK expects the response to be an array directly, not an object with a history property
    return [];
  }

  /**
   * Get thread state - required by LangGraph SDK
   */
  @Get('threads/:threadId/state')
  async getThreadState(@Param('threadId') threadId: string) {
    return {
      values: {
        messages: [],
        query_list: [],
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: 3,
        max_research_loops: 2,
        research_loop_count: 0,
        reasoning_model: 'gemini-2.5-pro-preview-05-06',
      },
      next: [],
      config: {
        configurable: {},
      },
      metadata: {},
      created_at: new Date().toISOString(),
      parent_config: null,
    };
  }

  /**
   * Update thread state - required by LangGraph SDK
   */
  @Post('threads/:threadId/state')
  async updateThreadState(
    @Param('threadId') threadId: string,
    @Body() body: any,
  ) {
    // In a real implementation, this would update the thread state in a database
    // For now, we'll just return a success response
    return {
      success: true,
      updated_at: new Date().toISOString(),
    };
  }
}
