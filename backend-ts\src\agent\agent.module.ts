import { Module } from '@nestjs/common';
import { GraphService } from './graph.service';
import { ConfigurationService } from './configuration.service';
import { AgentController } from './agent.controller';
import { LangGraphController } from './langgraph.controller';

@Module({
  controllers: [AgentController, LangGraphController],
  providers: [GraphService, ConfigurationService],
  exports: [GraphService],
})
export class AgentModule {}
