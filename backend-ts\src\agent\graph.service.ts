import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StateGraph, START, END, Send } from '@langchain/langgraph';
import { AIMessage } from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';
import { GoogleGenAI } from '@google/genai';

import { OverallStateAnnotation, OverallState, SourceInfo, ReflectionState } from './state';
import { ConfigurationService } from './configuration.service';
import {
  getCurrentDate,
  queryWriterInstructions,
  webSearcherInstructions,
  reflectionInstructions,
  answerInstructions
} from './prompts';
import {
  getResearchTopic,
  resolveUrls,
  getCitations,
  insertCitationMarkers,
} from './utils';

/**
 * Query generation state for intermediate processing
 */
interface QueryGenerationState {
  query_list: Array<{ query: string; rationale: string }>;
}



/**
 * Web search state for individual search operations
 */
interface WebSearchState {
  search_query: string;
  id: number;
}

/**
 * LangGraph service that implements the agent workflow.
 * This is the core implementation that replaces the Python graph.py
 */
@Injectable()
export class GraphService {
  private genaiClient: GoogleGenAI;
  private graph: any;

  constructor(
    private configService: ConfigService,
    private configurationService: ConfigurationService,
  ) {
    // Disable LangSmith tracing completely
    process.env.LANGCHAIN_TRACING_V2 = 'false';
    process.env.LANGCHAIN_API_KEY = '';

    // Check for GEMINI_API_KEY
    const geminiApiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY is not set');
    }

    // Initialize Google Gen AI client
    this.genaiClient = new GoogleGenAI({ apiKey: geminiApiKey });

    // Build the graph
    this.buildGraph();
  }

  /**
   * Build the LangGraph state graph
   */
  private buildGraph() {
    const builder = new StateGraph(OverallStateAnnotation)
      .addNode('generate_query', this.generateQuery.bind(this))
      .addNode('web_research', this.webResearch.bind(this))
      .addNode('reflection', this.reflection.bind(this))
      .addNode('finalize_answer', this.finalizeAnswer.bind(this))
      .addEdge(START, 'generate_query')
      .addConditionalEdges(
        'generate_query',
        this.continueToWebResearch.bind(this),
        ['web_research']
      )
      .addEdge('web_research', 'reflection')
      .addConditionalEdges(
        'reflection',
        this.evaluateResearch.bind(this),
        ['web_research', 'finalize_answer']
      )
      .addEdge('finalize_answer', END);

    this.graph = builder.compile();
  }

  /**
   * Get the compiled graph for external use
   */
  getGraph() {
    return this.graph;
  }

  /**
   * Wrapper methods for utility functions to maintain consistency
   */
  private resolveUrls(urlsToResolve: any[], id: number): Record<string, string> {
    return resolveUrls(urlsToResolve, id);
  }

  private getCitations(response: any, resolvedUrlsMap: Record<string, string>) {
    return getCitations(response, resolvedUrlsMap);
  }

  private insertCitationMarkers(text: string, citations: any[]) {
    return insertCitationMarkers(text, citations);
  }

  /**
   * LangGraph node that generates search queries based on the user's question.
   * Equivalent to the Python generate_query function.
   * Uses the official Google Gen AI SDK with structured output.
   */
  private async generateQuery(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);

      // Check for custom initial search query count (matching Python)
      const initialSearchQueryCount = state.initial_search_query_count ?? configurable.numberOfInitialQueries;

      // Format the prompt (matching Python)
      const currentDate = getCurrentDate();
      const formattedPrompt = queryWriterInstructions
        .replace('{current_date}', currentDate)
        .replace('{research_topic}', getResearchTopic(state.messages))
        .replace('{number_queries}', initialSearchQueryCount.toString());

      // Use Google Gen AI client with structured output (matching Python approach)
      const response = await this.genaiClient.models.generateContent({
        model: configurable.queryGeneratorModel,
        contents: formattedPrompt,
        config: {
          temperature: 1.0,
          responseMimeType: 'application/json',
          responseSchema: {
            type: 'object',
            properties: {
              query: {
                type: 'array',
                items: { type: 'string' },
                description: 'A list of search queries to be used for web research.'
              },
              rationale: {
                type: 'string',
                description: 'A brief explanation of why these queries are relevant to the research topic.'
              }
            },
            required: ['query', 'rationale'],
            propertyOrdering: ['query', 'rationale']
          }
        },
      });

      // Parse the structured response
      const candidate = response.candidates?.[0];
      if (!candidate?.content?.parts?.[0]?.text) {
        throw new Error('No valid response from Google Gen AI');
      }

      let responseText = candidate.content.parts[0].text;

      // Clean up response text - remove markdown code blocks if present
      responseText = responseText.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();

      const result = JSON.parse(responseText);

      return {
        // Store the query list for the next step (matching Python)
        query_list: result.query || ['General information search'],
      };
    } catch (error) {
      console.error('Error in generateQuery:', error);
      // Fallback to default queries if generation fails (matching Python behavior)
      return {
        query_list: ['General information search'],
      };
    }
  }

  /**
   * LangGraph conditional edge that sends search queries to web research nodes.
   * Equivalent to the Python continue_to_web_research function.
   */
  private continueToWebResearch(state: OverallState): Send[] {
    return (state.query_list || []).map((searchQuery, idx) =>
      new Send('web_research', { search_query: searchQuery, id: idx })
    );
  }

  /**
   * LangGraph node that performs web research using Google Search API.
   * Equivalent to the Python web_research function.
   * Uses the official Google Gen AI SDK with Google Search tool exactly like Python.
   */
  private async webResearch(
    state: WebSearchState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);

      // Format the prompt for comprehensive research (matching Python)
      const formattedPrompt = webSearcherInstructions
        .replace('{current_date}', getCurrentDate())
        .replace('{research_topic}', state.search_query);

      // Use Google Gen AI client with Google Search tool (updated for latest @google/genai SDK)
      const response = await this.genaiClient.models.generateContent({
        model: configurable.queryGeneratorModel,
        contents: formattedPrompt,
        config: {
          tools: [{ googleSearch: {} }],
          temperature: 0,
        },
      });

      // Extract response content
      const candidate = response.candidates?.[0];
      if (!candidate) {
        throw new Error('No response candidate received from Google Gen AI');
      }

      const responseText = candidate.content?.parts?.[0]?.text || '';

      // Process grounding metadata for citations (matching Python implementation)
      const resolvedUrls = this.resolveUrls(
        candidate.groundingMetadata?.groundingChunks || [],
        parseInt(state.id?.toString() || '0')
      );

      const citations = this.getCitations(response, resolvedUrls);
      const citedText = this.insertCitationMarkers(responseText, citations);

      // Extract sources from grounding metadata (matching Python implementation)
      const sourcesGathered: SourceInfo[] = Object.entries(resolvedUrls).map(([originalUrl, shortUrl]) => ({
        label: originalUrl.split('/').pop() || 'Source',
        short_url: shortUrl,
        value: originalUrl,
      }));

      return {
        sources_gathered: sourcesGathered,
        search_query: [state.search_query],
        web_research_result: [citedText],
      };
    } catch (error) {
      console.error('Error in webResearch:', error);

      // Fallback: Return basic response with error indication
      return {
        sources_gathered: [],
        search_query: [state.search_query],
        web_research_result: [`Research summary for: ${state.search_query}. Based on general knowledge, this topic requires further investigation. The system encountered an issue accessing current web information.`],
      };
    }
  }



  /**
   * LangGraph node that identifies knowledge gaps and generates follow-up queries.
   * Equivalent to the Python reflection function.
   * Uses the official Google Gen AI SDK with structured output.
   */
  private async reflection(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState> & { _reflection_result: ReflectionState }> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);

      // Increment the research loop count (matching Python)
      const researchLoopCount = (state.research_loop_count ?? 0) + 1;
      const reasoningModel = state.reasoning_model ?? configurable.reflectionModel;

      // Format the prompt (matching Python)
      const currentDate = getCurrentDate();
      const formattedPrompt = reflectionInstructions
        .replace('{current_date}', currentDate)
        .replace('{research_topic}', getResearchTopic(state.messages))
        .replace('{summaries}', state.web_research_result.join('\n\n---\n\n'));

      // Use Google Gen AI client with structured output (matching Python approach)
      const response = await this.genaiClient.models.generateContent({
        model: reasoningModel,
        contents: formattedPrompt,
        config: {
          temperature: 1.0,
          responseMimeType: 'application/json',
          responseSchema: {
            type: 'object',
            properties: {
              is_sufficient: {
                type: 'boolean',
                description: 'Whether the provided summaries are sufficient to answer the user\'s question.'
              },
              knowledge_gap: {
                type: 'string',
                description: 'A description of what information is missing or needs clarification.'
              },
              follow_up_queries: {
                type: 'array',
                items: { type: 'string' },
                description: 'A list of follow-up queries to address the knowledge gap.'
              }
            },
            required: ['is_sufficient', 'knowledge_gap', 'follow_up_queries'],
            propertyOrdering: ['is_sufficient', 'knowledge_gap', 'follow_up_queries']
          }
        },
      });

      // Parse the structured response
      const candidate = response.candidates?.[0];
      if (!candidate?.content?.parts?.[0]?.text) {
        throw new Error('No valid response from Google Gen AI');
      }

      let responseText = candidate.content.parts[0].text;

      // Clean up response text - remove markdown code blocks if present
      responseText = responseText.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();

      const result = JSON.parse(responseText);

      // Create reflection state (matching Python)
      const reflectionState: ReflectionState = {
        is_sufficient: result.is_sufficient,
        knowledge_gap: result.knowledge_gap,
        follow_up_queries: result.follow_up_queries,
        research_loop_count: researchLoopCount,
        number_of_ran_queries: state.search_query.length,
        max_research_loops: state.max_research_loops ?? configurable.maxResearchLoops,
      };

      // Return state update with reflection result stored for evaluation
      return {
        research_loop_count: researchLoopCount,
        _reflection_result: reflectionState,
      };
    } catch (error) {
      console.error('Error in reflection:', error);

      // Fallback: assume research is sufficient to avoid infinite loops
      const reflectionState: ReflectionState = {
        is_sufficient: true,
        knowledge_gap: 'Unable to determine knowledge gaps due to processing error',
        follow_up_queries: [],
        research_loop_count: (state.research_loop_count ?? 0) + 1,
        number_of_ran_queries: state.search_query.length,
        max_research_loops: state.max_research_loops ?? 2,
      };

      return {
        research_loop_count: reflectionState.research_loop_count,
        _reflection_result: reflectionState,
      };
    }
  }

  /**
   * LangGraph routing function that determines the next step in the research flow.
   * Equivalent to the Python evaluate_research function.
   */
  private evaluateResearch(
    state: OverallState & { _reflection_result?: ReflectionState },
    config?: RunnableConfig
  ): string | Send[] {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const maxResearchLoops = state.max_research_loops ?? configurable.maxResearchLoops;

    // Get reflection result from state
    const reflectionResult = state._reflection_result;
    if (!reflectionResult) {
      return 'finalize_answer'; // Fallback if no reflection data
    }

    if (reflectionResult.is_sufficient || reflectionResult.research_loop_count >= maxResearchLoops) {
      return 'finalize_answer';
    } else {
      return reflectionResult.follow_up_queries.map((followUpQuery: string, idx: number) =>
        new Send('web_research', {
          search_query: followUpQuery,
          id: reflectionResult.number_of_ran_queries + idx,
        })
      );
    }
  }

  /**
   * LangGraph node that finalizes the research summary.
   * Equivalent to the Python finalize_answer function.
   * Uses the official Google Gen AI SDK.
   */
  private async finalizeAnswer(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);
      const reasoningModel = state.reasoning_model ?? configurable.answerModel;

      // Format the prompt (matching Python)
      const currentDate = getCurrentDate();
      const formattedPrompt = answerInstructions
        .replace('{current_date}', currentDate)
        .replace('{research_topic}', getResearchTopic(state.messages))
        .replace('{summaries}', state.web_research_result.join('\n---\n\n'));

      // Use Google Gen AI client (matching Python)
      const response = await this.genaiClient.models.generateContent({
        model: reasoningModel,
        contents: formattedPrompt,
        config: {
          temperature: 0,
        },
      });

      // Extract response content
      const candidate = response.candidates?.[0];
      if (!candidate?.content?.parts?.[0]?.text) {
        throw new Error('No valid response from Google Gen AI');
      }

      let content = candidate.content.parts[0].text;

      // Replace short URLs with original URLs and collect used sources (matching Python)
      const uniqueSources: SourceInfo[] = [];

      for (const source of state.sources_gathered) {
        if (content.includes(source.short_url)) {
          content = content.replace(new RegExp(source.short_url, 'g'), source.value);
          uniqueSources.push(source);
        }
      }

      return {
        messages: [new AIMessage({ content })],
        sources_gathered: uniqueSources,
      };
    } catch (error) {
      console.error('Error in finalizeAnswer:', error);

      // Fallback: Return basic answer
      const fallbackContent = `Based on the research conducted, here's what I found about ${getResearchTopic(state.messages)}:\n\n${state.web_research_result.join('\n\n')}`;

      return {
        messages: [new AIMessage({ content: fallbackContent })],
        sources_gathered: state.sources_gathered,
      };
    }
  }
}
