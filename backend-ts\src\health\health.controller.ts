import { Controller, Get } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Health check controller for production monitoring.
 * Provides endpoints to check the health status of the application.
 */
@Controller('health')
export class HealthController {
  constructor(private configService: ConfigService) {}

  /**
   * Basic health check endpoint.
   * Returns 200 OK if the application is running.
   */
  @Get()
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: this.configService.get('NODE_ENV', 'development'),
    };
  }

  /**
   * Detailed health check endpoint.
   * Checks various system components and dependencies.
   */
  @Get('detailed')
  getDetailedHealth() {
    const geminiApiKey = this.configService.get('GEMINI_API_KEY');
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: this.configService.get('NODE_ENV', 'development'),
      version: process.env.npm_package_version || '0.0.1',
      node_version: process.version,
      memory: process.memoryUsage(),
      checks: {
        gemini_api_key: geminiApiKey ? 'configured' : 'missing',
        environment_variables: {
          query_generator_model: this.configService.get('QUERY_GENERATOR_MODEL', 'default'),
          reflection_model: this.configService.get('REFLECTION_MODEL', 'default'),
          answer_model: this.configService.get('ANSWER_MODEL', 'default'),
        },
      },
    };
  }

  /**
   * Readiness probe endpoint.
   * Checks if the application is ready to serve traffic.
   */
  @Get('ready')
  getReadiness() {
    const geminiApiKey = this.configService.get('GEMINI_API_KEY');
    
    if (!geminiApiKey) {
      return {
        status: 'not_ready',
        reason: 'GEMINI_API_KEY not configured',
        timestamp: new Date().toISOString(),
      };
    }

    return {
      status: 'ready',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Liveness probe endpoint.
   * Checks if the application is alive and responsive.
   */
  @Get('live')
  getLiveness() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
