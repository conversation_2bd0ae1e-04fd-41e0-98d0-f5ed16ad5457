import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { ZodSchema } from 'zod';

/**
 * Validation pipe using Zod schemas.
 * Provides FastAPI-like automatic validation for request bodies.
 */
@Injectable()
export class ZodValidationPipe implements PipeTransform {
  constructor(private schema: ZodSchema) {}

  transform(value: any, metadata: ArgumentMetadata) {
    try {
      // Handle different input types that might come from Fastify
      let parsedInput = value;

      // If value is a string, try to parse it as JSON
      if (typeof value === 'string') {
        try {
          parsedInput = JSON.parse(value);
        } catch (parseError) {
          // If it's not valid JSON, the validation will fail appropriately
          // Don't modify the value, let <PERSON><PERSON> handle the error
        }
      }

      // If value is a Buffer (common with Fastify), convert to string and parse
      if (Buffer.isBuffer(value)) {
        try {
          const stringValue = value.toString('utf8');
          parsedInput = JSON.parse(stringValue);
        } catch (parseError) {
          // If parsing fails, keep the original value
        }
      }

      // If value is already an object, use it directly
      if (typeof value === 'object' && value !== null && !Buffer.isBuffer(value)) {
        parsedInput = value;
      }

      const parsedValue = this.schema.parse(parsedInput);
      return parsedValue;
    } catch (error: any) {
      // Format Zod errors properly to match the expected error format
      const formattedErrors = error.errors || [{
        code: 'validation_error',
        message: error.message || 'Unknown validation error',
        path: [],
      }];

      throw new BadRequestException({
        detail: 'Validation failed',
        status_code: 400,
        timestamp: new Date().toISOString(),
        path: metadata.data || 'unknown',
        method: 'POST',
        errors: formattedErrors,
      });
    }
  }
}
